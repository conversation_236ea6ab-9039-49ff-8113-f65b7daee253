

[workspace]
members = [
    "examples"
    # 可在此添加其它成员路径
]

[workspace.dependencies]
thiserror = "1.0"
tracing = { version = "0.1.40", default-features = false }
core_affinity = "0.8"
thread-priority = "0.3"
ash = "0.37.2"
ash-window = "0.12"
raw-window-handle = "0.5"
nalgebra-glm = "0.18"
tobj = "4.0"
tracing-subscriber = "0.3.18"
futures = "0.3.31"
tokio = { version = "1.45.1", features = ["rt-multi-thread"] }
anyhow = "1.0.68"
bytemuck = "1.13.0"
gpu-allocator = "0.21.0"
softbuffer = { version = "0.4.6", default-features = false, features = [
    "x11",
    "x11-dlopen",
    "wayland",
    "wayland-dlopen",
] }
winit = "0.30.11"