use crate::backend::driver_enums::*;
use std::any::Any;

/// Platform负责处理平台特定的窗口系统和图形API初始化
/// 它是Driver与操作系统和窗口系统之间的桥梁
pub trait Platform: Any + Send + Sync {
    /// 获取平台类型
    fn get_backend_type(&self) -> Backend;

    /// 初始化平台
    fn init(&mut self) -> Result<(), String>;

    /// 清理资源
    fn cleanup(&mut self);

    /// 创建交换链
    fn create_swap_chain(&mut self, window: &dyn Any, flags: u64) -> Result<Box<dyn Any>, String>;

    /// 销毁交换链
    fn destroy_swap_chain(&mut self, swap_chain: Box<dyn Any>);

    /// 创建定时器查询
    fn create_timer_query(&mut self) -> Result<Box<dyn Any>, String>;

    /// 获取平台特定的上下文
    fn get_platform_context(&self) -> &dyn Any;

    /// 设置呈现时间
    fn set_presentation_time(&mut self, time_ns: i64);

    /// 检查交换链是否需要调整大小
    fn has_resized(&self, swap_chain: &dyn Any) -> bool;

    /// 重新创建交换链
    fn recreate_swap_chain(&mut self, swap_chain: &mut dyn Any);

    /// 等待平台空闲
    fn wait_idle(&self);
}

/// 平台工厂，用于创建不同类型的平台实例
pub struct PlatformFactory;

impl PlatformFactory {
    /// 创建指定类型的平台实例
    pub fn create<T: 'static>(backend: Backend, window: &T) -> Result<Box<dyn Platform>, String> {
        // 解析后端类型（如果是Default，则选择平台默认值）
        let resolved_backend = backend.resolve();

        // 检查窗口参数是否为()
        if std::any::TypeId::of::<T>() == std::any::TypeId::of::<()>() {
            // 如果窗口参数是()，则创建一个没有窗口的平台
            // 这里我们暂时返回错误，因为我们还没有实现无窗口的平台创建
            return Err("Creating platform without window is not implemented yet".to_string());
        }

        match resolved_backend {
            Backend::Vulkan => {
                // 创建VulkanPlatform
                return Ok(Box::new(crate::backend::vulkan::platform::VulkanPlatform::new_from_any(window as &dyn Any)?));
            },
            Backend::OpenGL => {
                #[cfg(feature = "opengl")]
                {
                    // 这里需要导入并返回OpenGLPlatform的实例
                    // return Ok(Box::new(crate::backend::opengl::OpenGLPlatform::new(window)));
                    return Err("OpenGL backend is not implemented yet".to_string());
                }

                #[cfg(not(feature = "opengl"))]
                {
                    return Err("OpenGL backend is not enabled in this build".to_string());
                }
            },
            
            Backend::WebGPU => {
                #[cfg(feature = "webgpu")]
                {
                    // 这里需要导入并返回WebGPUPlatform的实例
                    // return Ok(Box::new(crate::backend::webgpu::WebGPUPlatform::new(window)));
                    return Err("WebGPU backend is not implemented yet".to_string());
                }

                #[cfg(not(feature = "webgpu"))]
                {
                    return Err("WebGPU backend is not enabled in this build".to_string());
                }
            },
            Backend::NoOp => {
                // 返回一个空实现，用于测试
                return Err("NoOp backend is not implemented yet".to_string());
            },
            _ => {
                return Err(format!("Unsupported backend: {}", backend));
            }
        }
    }
}
