/*
use crate::backend::vulkan::driver::VulkanDriver;
use crate::backend::vulkan::texture::Texture;
use ash::{util::Align, vk, Device};
use nalgebra_glm as glm;
use std::mem::{align_of, size_of};

#[derive(<PERSON><PERSON>, Co<PERSON>)]
#[allow(dead_code)]
pub struct UniformBufferObject {
    pub(crate) model: glm::Mat4,
    pub(crate) view: glm::Mat4,
    pub(crate) proj: glm::Mat4,
}

impl UniformBufferObject {
    pub fn get_descriptor_set_layout_binding<'a>() -> vk::DescriptorSetLayoutBinding<'a> {
        vk::DescriptorSetLayoutBinding::default()
            .binding(0)
            .descriptor_type(vk::DescriptorType::UNIFORM_BUFFER)
            .descriptor_count(1)
            .stage_flags(vk::ShaderStageFlags::VERTEX)
        // .immutable_samplers() null since we're not creating a sampler descriptor
    }

    /// Create uniform buffers for each swapchain image
    pub fn create_uniform_buffers(
        vulkan_driver: &VulkanDriver,
        count: usize,
    ) -> (Vec<vk::Buffer>, Vec<vk::DeviceMemory>) {
        // 获取设备和上下文
        let device = vulkan_driver.device().expect("Device not available");
        let context = vulkan_driver.context().expect("Context not available");

        let size = size_of::<UniformBufferObject>() as vk::DeviceSize;
        let mut buffers = Vec::new();
        let mut memories = Vec::new();

        for _ in 0..count {
            // 创建缓冲区
            let buffer_info = vk::BufferCreateInfo::default()
                .size(size)
                .usage(vk::BufferUsageFlags::UNIFORM_BUFFER)
                .sharing_mode(vk::SharingMode::EXCLUSIVE);

            let buffer = unsafe {
                device.create_buffer(&buffer_info, None).unwrap()
            };

            // 获取内存需求
            let mem_requirements = unsafe {
                device.get_buffer_memory_requirements(buffer)
            };

            // 分配内存
            let mem_type = Self::find_memory_type(
                mem_requirements,
                context.get_mem_properties(),
                vk::MemoryPropertyFlags::HOST_VISIBLE | vk::MemoryPropertyFlags::HOST_COHERENT,
            );

            let alloc_info = vk::MemoryAllocateInfo::default()
                .allocation_size(mem_requirements.size)
                .memory_type_index(mem_type);

            let memory = unsafe {
                device.allocate_memory(&alloc_info, None).unwrap()
            };

            // 绑定内存到缓冲区
            unsafe {
                device.bind_buffer_memory(buffer, memory, 0).unwrap()
            };
            buffers.push(buffer);
            memories.push(memory);
        }

        (buffers, memories)
    }

    /// Update the uniform buffer with new data
    pub fn update_uniform_buffer(
        device: &Device,
        buffer_memory: vk::DeviceMemory,
        ubo: &UniformBufferObject,
    ) {
        let size = size_of::<UniformBufferObject>() as vk::DeviceSize;
        let ubos = [*ubo];

        unsafe {
            let data_ptr = device
                .map_memory(buffer_memory, 0, size, vk::MemoryMapFlags::empty())
                .unwrap();
            let mut align = Align::new(data_ptr, align_of::<f32>() as _, size);
            align.copy_from_slice(&ubos);
            device.unmap_memory(buffer_memory);
        }
    }

    /// Create a descriptor set layout for uniform buffers
    pub fn create_descriptor_set_layout(device: &Device) -> vk::DescriptorSetLayout {
        let ubo_binding = Self::get_descriptor_set_layout_binding();
        let sampler_binding = vk::DescriptorSetLayoutBinding::default()
            .binding(1)
            .descriptor_count(1)
            .descriptor_type(vk::DescriptorType::COMBINED_IMAGE_SAMPLER)
            .stage_flags(vk::ShaderStageFlags::FRAGMENT);
        let bindings = [ubo_binding, sampler_binding];

        let layout_info = vk::DescriptorSetLayoutCreateInfo::default().bindings(&bindings);

        unsafe {
            device
                .create_descriptor_set_layout(&layout_info, None)
                .unwrap()
        }
    }

    /// Create a descriptor pool for uniform buffers
    pub fn create_descriptor_pool(device: &Device, size: u32) -> vk::DescriptorPool {
        let ubo_pool_size = vk::DescriptorPoolSize {
            ty: vk::DescriptorType::UNIFORM_BUFFER,
            descriptor_count: size,
        };
        let sampler_pool_size = vk::DescriptorPoolSize {
            ty: vk::DescriptorType::COMBINED_IMAGE_SAMPLER,
            descriptor_count: size,
        };

        let pool_sizes = [ubo_pool_size, sampler_pool_size];

        let pool_info = vk::DescriptorPoolCreateInfo::default()
            .pool_sizes(&pool_sizes)
            .max_sets(size);

        unsafe { device.create_descriptor_pool(&pool_info, None).unwrap() }
    }

    /// Create descriptor sets for uniform buffers
    pub fn create_descriptor_sets(
        device: &Device,
        pool: vk::DescriptorPool,
        layout: vk::DescriptorSetLayout,
        uniform_buffers: &[vk::Buffer],
        texture: Texture,
    ) -> Vec<vk::DescriptorSet> {
        let layouts = (0..uniform_buffers.len())
            .map(|_| layout)
            .collect::<Vec<_>>();
        let alloc_info = vk::DescriptorSetAllocateInfo::default()
            .descriptor_pool(pool)
            .set_layouts(&layouts);
        let descriptor_sets = unsafe { device.allocate_descriptor_sets(&alloc_info).unwrap() };

        descriptor_sets
            .iter()
            .zip(uniform_buffers.iter())
            .for_each(|(set, buffer)| {
                let buffer_info = vk::DescriptorBufferInfo::default()
                    .buffer(*buffer)
                    .offset(0)
                    .range(size_of::<UniformBufferObject>() as vk::DeviceSize);
                let buffer_infos = [buffer_info];

                let image_info = vk::DescriptorImageInfo::default()
                    .image_layout(vk::ImageLayout::SHADER_READ_ONLY_OPTIMAL)
                    .image_view(texture.view)
                    .sampler(texture.sampler.unwrap());
                let image_infos = [image_info];

                let ubo_descriptor_write = vk::WriteDescriptorSet::default()
                    .dst_set(*set)
                    .dst_binding(0)
                    .dst_array_element(0)
                    .descriptor_type(vk::DescriptorType::UNIFORM_BUFFER)
                    .buffer_info(&buffer_infos);
                let sampler_descriptor_write = vk::WriteDescriptorSet::default()
                    .dst_set(*set)
                    .dst_binding(1)
                    .dst_array_element(0)
                    .descriptor_type(vk::DescriptorType::COMBINED_IMAGE_SAMPLER)
                    .image_info(&image_infos);

                let descriptor_writes = [ubo_descriptor_write, sampler_descriptor_write];

                unsafe { device.update_descriptor_sets(&descriptor_writes, &[]) }
            });

        descriptor_sets
    }

    // 注意：create_buffer 方法已被内联到 create_uniform_buffers 方法中

    /// Find a memory type in `mem_properties` that is suitable
    /// for `requirements` and supports `required_properties`.
    ///
    /// # Returns
    ///
    /// The index of the memory type from `mem_properties`.
    fn find_memory_type(
        requirements: vk::MemoryRequirements,
        mem_properties: vk::PhysicalDeviceMemoryProperties,
        required_properties: vk::MemoryPropertyFlags,
    ) -> u32 {
        for i in 0..mem_properties.memory_type_count {
            if requirements.memory_type_bits & (1 << i) != 0
                && mem_properties.memory_types[i as usize]
                    .property_flags
                    .contains(required_properties)
            {
                return i;
            }
        }
        panic!("Failed to find suitable memory type.")
    }
}
*/
