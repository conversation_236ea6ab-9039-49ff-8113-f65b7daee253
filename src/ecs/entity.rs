/// Entity is a lightweight wrapper around the u32

pub(crate) type EntityType = u32;
pub(crate) type EntityTypeInAndroid = i32;

#[derive(<PERSON>bug, <PERSON><PERSON>ult, <PERSON>py, Clone, Eq, PartialEq, Ord, PartialOrd, Hash)]
pub struct Entity(EntityType);

impl Entity {
    /// Entity creation should be fully controlled by the EntityManager
    pub(crate) fn set_id(&mut self, id: EntityType) {
        self.0 = id;
    }

    /// Get id
    pub fn id(&self) -> EntityType {
        self.0
    }

    /// Returns true if this is a null entity.
    pub fn is_null(&self) -> bool {
        self.0 == 0
    }

    /// Returns true if this is not a null entity
    pub fn is_valid(&self) -> bool {
        !self.is_null()
    }

    /// Clear the entity ID
    pub fn clear(&mut self) {
        self.0 = 0;
    }

    /// Export an entity's ID to an Android-compatible type.
    pub fn smuggle(entity: Entity) -> EntityTypeInAndroid {
        entity.id() as i32
    }

    /// Import an entity from an i32 generated by smuggle() above
    pub fn import(id: EntityTypeInAndroid) -> Entity {
        Entity(id as u32)
    }
}
