//! Memory area policies for allocators.
//!
//! This module provides different memory area policies that can be used with allocators:
//! - `StaticArea`: Uses a pre-allocated memory region
//! - `HeapArea`: Allocates memory from the heap
//! - `NullArea`: Represents an empty area (for testing/placeholder)

use std::alloc::{Layout, alloc, dealloc};
use std::ptr::NonNull;

/// A memory area that can be used by allocators.
pub trait Area {
    /// Get a pointer to the beginning of the area.
    fn begin(&self) -> *mut u8;

    /// Get a pointer to the end of the area.
    fn end(&self) -> *mut u8;

    /// Get the size of the area in bytes.
    fn size(&self) -> usize {
        unsafe { self.end().offset_from(self.begin()) as usize }
    }
}

/// A static memory area that uses a pre-allocated memory region.
pub struct StaticArea {
    begin: *mut u8,
    end: *mut u8,
}

impl StaticArea {
    /// Create a new static area from the given memory region.
    ///
    /// # Safety
    ///
    /// The caller must ensure that the memory region is valid and remains valid
    /// for the lifetime of the `StaticArea`.
    pub unsafe fn new(begin: *mut u8, end: *mut u8) -> Self {
        debug_assert!(begin <= end);
        Self { begin, end }
    }

    /// Create a new static area from a slice.
    pub fn from_slice(slice: &mut [u8]) -> Self {
        let begin = slice.as_mut_ptr();
        let end = unsafe { begin.add(slice.len()) };
        Self { begin, end }
    }
}

impl Area for StaticArea {
    fn begin(&self) -> *mut u8 {
        self.begin
    }

    fn end(&self) -> *mut u8 {
        self.end
    }
}

/// A heap-allocated memory area.
pub struct HeapArea {
    ptr: Option<NonNull<u8>>,
    size: usize,
    layout: Layout,
}

impl HeapArea {
    /// Create a new heap area with the specified size.
    pub fn new(size: usize) -> Self {
        if size == 0 {
            return Self {
                ptr: None,
                size: 0,
                layout: Layout::from_size_align(0, 1).unwrap(),
            };
        }

        // Align to cache line for better performance
        let layout = Layout::from_size_align(size, 64)
            .unwrap_or_else(|_| Layout::from_size_align(size, 8).unwrap());

        let ptr = unsafe { alloc(layout) };
        let ptr = NonNull::new(ptr)
            .unwrap_or_else(|| panic!("Failed to allocate memory for HeapArea of size {}", size));

        Self {
            ptr: Some(ptr),
            size,
            layout,
        }
    }
}

impl Area for HeapArea {
    fn begin(&self) -> *mut u8 {
        match self.ptr {
            Some(ptr) => ptr.as_ptr(),
            None => std::ptr::null_mut(),
        }
    }

    fn end(&self) -> *mut u8 {
        match self.ptr {
            Some(ptr) => unsafe { ptr.as_ptr().add(self.size) },
            None => std::ptr::null_mut(),
        }
    }

    fn size(&self) -> usize {
        self.size
    }
}

impl Drop for HeapArea {
    fn drop(&mut self) {
        if let Some(ptr) = self.ptr {
            unsafe {
                dealloc(ptr.as_ptr(), self.layout);
            }
        }
    }
}

/// A null memory area (empty).
pub struct NullArea;

impl Area for NullArea {
    fn begin(&self) -> *mut u8 {
        std::ptr::null_mut()
    }

    fn end(&self) -> *mut u8 {
        std::ptr::null_mut()
    }

    fn size(&self) -> usize {
        0
    }
}

impl Default for NullArea {
    fn default() -> Self {
        NullArea
    }
}
