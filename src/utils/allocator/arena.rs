use std::{
    cell::UnsafeCell,
    mem,
    ops::{<PERSON><PERSON>, DerefMut},
    sync::{Mutex, MutexGuard},
};

use crate::utils::allocator::{
    area::HeapA<PERSON>,
    heap::HeapAllocator,
    tracking::Untracked,
    Allocator,
};

pub trait Lock<T> {
    type Guard<'a>: Deref<Target = T> + DerefMut
    where
        Self: 'a;

    fn lock(&self) -> Self::Guard<'_>;
}

pub struct MutexLock<T>(Mutex<T>);

impl<T> Lock<T> for MutexLock<T> {
    type Guard<'a>
        = MutexGuard<'a, T>
    where
        T: 'a;

    fn lock(&self) -> Self::Guard<'_> {
        // In a real application, you might want to handle poisoning
        // or use try_lock() for non-blocking behavior
        self.0.lock().unwrap()
    }
}

impl<T> MutexLock<T> {
    pub fn new(data: T) -> Self {
        Self(Mutex::new(data))
    }
}

pub struct NoLock<T>(UnsafeCell<T>);

impl<T> Lock<T> for NoLock<T> {
    type Guard<'a>
        = &'a mut T
    where
        Self: 'a;
    fn lock(&self) -> Self::Guard<'_> {
        unsafe { &mut *self.0.get() }
    }
}

impl<T> NoLock<T> {
    pub fn new(data: T) -> Self {
        Self(UnsafeCell::new(data))
    }
}

/// A generic arena allocator with configurable policies.
///
/// This arena provides memory allocation with support for:
/// - Custom locking policies (thread-safe vs single-threaded)
/// - Memory tracking and debugging
/// - Different memory area policies
/// - Automatic resource management
pub struct Arena<A, LockingPolicy, TrackingPolicy = Untracked, AreaPolicy = HeapArea> {
    allocator: A,
    locking_policy: LockingPolicy,
    tracking_policy: TrackingPolicy,
    area_policy: AreaPolicy,
    name: Option<String>,
}

impl<A, LockingPolicy, TrackingPolicy, AreaPolicy>
    Arena<A, LockingPolicy, TrackingPolicy, AreaPolicy>
where
    A: Allocator,
    LockingPolicy: Lock<A>,
    TrackingPolicy: crate::utils::allocator::tracking::Tracker,
    AreaPolicy: crate::utils::allocator::area::Area,
{
    pub fn new(
        allocator: A,
        locking_policy: LockingPolicy,
        tracking_policy: TrackingPolicy,
        area_policy: AreaPolicy,
    ) -> Self {
        Self {
            allocator,
            locking_policy,
            tracking_policy,
            area_policy,
            name: None,
        }
    }

    pub fn with_name(
        name: String,
        allocator: A,
        locking_policy: LockingPolicy,
        tracking_policy: TrackingPolicy,
        area_policy: AreaPolicy,
    ) -> Self {
        Self {
            allocator,
            locking_policy,
            tracking_policy,
            area_policy,
            name: Some(name),
        }
    }

    pub fn alloc(&mut self, size: usize, alignment: usize, offset: usize) -> Option<*mut u8> {
        let mut guard = self.locking_policy.lock();
        let ptr = guard.alloc(size, alignment, offset);
        if let Some(p) = ptr {
            self.tracking_policy.on_alloc(p, size, alignment, offset);
        }
        ptr
    }

    pub fn alloc_aligned(&mut self, size: usize, alignment: usize) -> Option<*mut u8> {
        self.alloc(size, alignment, 0)
    }

    pub fn alloc_default(&mut self, size: usize) -> Option<*mut u8> {
        self.alloc_aligned(size, mem::align_of::<usize>())
    }

    pub fn alloc_typed<T>(&mut self, count: usize, alignment: usize, offset: usize) -> Option<*mut T>
    where
        T: Sized,
    {
        let size = count * mem::size_of::<T>();
        self.alloc(size, alignment, offset).map(|p| p as *mut T)
    }

    pub fn alloc_typed_default<T>(&mut self, count: usize) -> Option<*mut T>
    where
        T: Sized,
    {
        self.alloc_typed(count, mem::align_of::<T>(), 0)
    }

    pub fn free(&mut self, ptr: *mut u8, size: usize) {
        if !ptr.is_null() {
            let mut guard = self.locking_policy.lock();
            self.tracking_policy.on_free(ptr, size);
            unsafe {
                guard.free(ptr, size);
            }
        }
    }

    pub fn free_typed<T>(&mut self, ptr: *mut T, count: usize)
    where
        T: Sized,
    {
        if !ptr.is_null() {
            let size = count * mem::size_of::<T>();
            self.free(ptr as *mut u8, size);
        }
    }

    pub fn reset(&mut self) {
        let _guard = self.locking_policy.lock();
        self.tracking_policy.on_reset();
    }

    pub fn get_current(&self) -> *mut u8 {
        std::ptr::null_mut()
    }

    pub fn rewind(&mut self, addr: *mut u8) {
        let _guard = self.locking_policy.lock();
        self.tracking_policy.on_rewind(addr);
    }

    pub fn make<T>(&mut self) -> Option<*mut T>
    where
        T: Sized + Default,
    {
        let ptr = self.alloc_typed_default::<T>(1)?;
        unsafe {
            ptr.write(T::default());
        }
        Some(ptr)
    }

    pub fn make_with<T, F>(&mut self, f: F) -> Option<*mut T>
    where
        T: Sized,
        F: FnOnce() -> T,
    {
        let ptr = self.alloc_typed_default::<T>(1)?;
        unsafe {
            ptr.write(f());
        }
        Some(ptr)
    }

    pub fn destroy<T>(&mut self, ptr: *mut T)
    where
        T: Sized,
    {
        if !ptr.is_null() {
            unsafe {
                ptr.drop_in_place();
            }
            self.free_typed(ptr, 1);
        }
    }

    pub fn get_allocator(&self) -> &A {
        &self.allocator
    }

    pub fn get_tracker(&self) -> &TrackingPolicy {
        &self.tracking_policy
    }

    pub fn get_tracker_mut(&mut self) -> &mut TrackingPolicy {
        &mut self.tracking_policy
    }

    pub fn get_area(&self) -> &AreaPolicy {
        &self.area_policy
    }

    pub fn get_area_mut(&mut self) -> &mut AreaPolicy {
        &mut self.area_policy
    }
}

pub type HeapArena<TrackingPolicy = Untracked> =
    Arena<HeapAllocator, NoLock<HeapAllocator>, TrackingPolicy, HeapArea>;

pub fn create_heap_arena<TrackingPolicy>(
    name: Option<String>,
) -> HeapArena<TrackingPolicy>
where
    TrackingPolicy: crate::utils::allocator::tracking::Tracker + Default,
{
    let allocator = HeapAllocator::new();
    let locking_policy = NoLock::new(allocator.clone());
    let tracking_policy = TrackingPolicy::default();
    let area_policy = HeapArea::new(0);
    if let Some(name) = name {
        Arena::with_name(
            name,
            allocator,
            locking_policy,
            tracking_policy,
            area_policy,
        )
    } else {
        Arena::new(allocator, locking_policy, tracking_policy, area_policy)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::utils::allocator::tracking::Untracked;

    #[test]
    fn test_arena_alloc_and_free() {
        let mut arena = create_heap_arena::<Untracked>(Some("test_arena".to_string()));
        let size = 64;
        let alignment = 8;
        let ptr = arena.alloc(size, alignment, 0);
        assert!(ptr.is_some());
        let ptr = ptr.unwrap();
        assert!(!ptr.is_null());
        unsafe {
            std::ptr::write_bytes(ptr, 0xAB, size);
        }
        arena.free(ptr, size);
    }

    #[test]
    fn test_arena_make_and_destroy() {
        #[derive(Default, PartialEq, Debug)]
        struct Foo(u32);
        let mut arena = create_heap_arena::<Untracked>(None);
        let foo_ptr = arena.make::<Foo>().unwrap();
        unsafe {
            assert_eq!((*foo_ptr).0, 0);
            (*foo_ptr).0 = 42;
        }
        arena.destroy(foo_ptr);
    }

    #[test]
    fn test_arena_alloc_typed() {
        let mut arena = create_heap_arena::<Untracked>(None);
        let arr = arena.alloc_typed_default::<u32>(10).unwrap();
        unsafe {
            for i in 0..10 {
                *arr.add(i) = i as u32;
            }
            for i in 0..10 {
                assert_eq!(*arr.add(i), i as u32);
            }
        }
        arena.free_typed(arr, 10);
    }

    #[test]
    fn test_arena_reset() {
        let mut arena = create_heap_arena::<Untracked>(None);
        let _ = arena.alloc(32, 8, 0);
        arena.reset();
        let _ = arena.alloc(32, 8, 0);
    }

    // --- Tests for ArenaScope ---

    #[test]
    fn test_arena_scope_basic() {
        let mut arena = create_heap_arena::<Untracked>(None);
        let initial_current = arena.get_current();

        {
            let mut scope = ArenaScope::new(&mut arena);
            
            // Allocate some memory in the scope
            let ptr1 = scope.allocate(64, 8).unwrap();
            let ptr2 = scope.allocate(32, 4).unwrap();
            
            assert!(!ptr1.is_null());
            assert!(!ptr2.is_null());
            assert_ne!(ptr1, ptr2);
        } // Scope ends here, memory should be automatically freed

        // Arena should be rewound to the initial state
        assert_eq!(arena.get_current(), initial_current);
    }

    #[test]
    fn test_arena_scope_with_objects() {
        #[derive(Debug, PartialEq)]
        struct TestObject {
            value: u32,
            name: String,
        }

        impl Drop for TestObject {
            fn drop(&mut self) {
                // This will be called when the scope ends
                println!("Dropping TestObject: {} = {}", self.name, self.value);
            }
        }

        let mut arena = create_heap_arena::<Untracked>(None);
        let initial_current = arena.get_current();

        {
            let mut scope = ArenaScope::new(&mut arena);
            
            // Create objects with automatic destruction
            let obj1 = scope.make(|| TestObject {
                value: 42,
                name: "first".to_string(),
            }).unwrap();
            
            let obj2 = scope.make(|| TestObject {
                value: 100,
                name: "second".to_string(),
            }).unwrap();

            unsafe {
                assert_eq!((*obj1).value, 42);
                assert_eq!((*obj2).value, 100);
            }
        } // Objects are automatically destroyed here

        // Arena should be rewound to the initial state
        assert_eq!(arena.get_current(), initial_current);
    }

    #[test]
    fn test_arena_scope_typed_allocation() {
        let mut arena = create_heap_arena::<Untracked>(None);
        let initial_current = arena.get_current();

        {
            let mut scope = ArenaScope::new(&mut arena);
            
            // Allocate typed memory
            let arr = scope.allocate_typed::<u32>(10, 4).unwrap();
            
            unsafe {
                for i in 0..10 {
                    *arr.add(i) = i as u32;
                }
                for i in 0..10 {
                    assert_eq!(*arr.add(i), i as u32);
                }
            }
        } // Memory is automatically freed

        // Arena should be rewound to the initial state
        assert_eq!(arena.get_current(), initial_current);
    }

    #[test]
    fn test_arena_scope_finalizer_order() {
        use std::sync::Mutex;
        use std::sync::Once;
        static INIT: Once = Once::new();
        static DROP_ORDER: Mutex<Vec<u32>> = Mutex::new(Vec::new());

        #[derive(Debug)]
        struct OrderedObject {
            id: u32,
        }

        impl Drop for OrderedObject {
            fn drop(&mut self) {
                if let Ok(mut order) = DROP_ORDER.lock() {
                    order.push(self.id);
                }
            }
        }

        let mut arena = create_heap_arena::<Untracked>(None);

        {
            let mut scope = ArenaScope::new(&mut arena);
            
            // Create objects in order
            let _obj1 = scope.make(|| OrderedObject { id: 1 }).unwrap();
            let _obj2 = scope.make(|| OrderedObject { id: 2 }).unwrap();
            let _obj3 = scope.make(|| OrderedObject { id: 3 }).unwrap();
        } // Objects should be destroyed in reverse order

        // Check that objects were destroyed in reverse order (LIFO)
        if let Ok(mut order) = DROP_ORDER.lock() {
            assert_eq!(*order, vec![3, 2, 1]);
            order.clear();
        }
    }
}

// ------------------------------------------------------------------------------------------------
// ArenaScope - Automatic Resource Management
// ------------------------------------------------------------------------------------------------

/// A scope-based arena allocator that automatically manages object lifetimes.
/// 
/// This is similar to C++'s ArenaScope, providing RAII semantics for arena allocations.
/// Objects created within the scope are automatically destroyed when the scope ends.
pub struct ArenaScope<'a, A, LockingPolicy, TrackingPolicy, AreaPolicy>
where
    A: Allocator,
    LockingPolicy: Lock<A>,
    TrackingPolicy: crate::utils::allocator::tracking::Tracker,
    AreaPolicy: crate::utils::allocator::area::Area,
{
    arena: &'a mut Arena<A, LockingPolicy, TrackingPolicy, AreaPolicy>,
    rewind_point: *mut u8,
    finalizers: Vec<Finalizer>,
}

/// A finalizer that will be called when the scope ends.
#[derive(Copy, Clone)]
struct Finalizer {
    finalizer: fn(*mut u8),
    data: *mut u8,
}

impl<'a, A, LockingPolicy, TrackingPolicy, AreaPolicy> ArenaScope<'a, A, LockingPolicy, TrackingPolicy, AreaPolicy>
where
    A: Allocator,
    LockingPolicy: Lock<A>,
    TrackingPolicy: crate::utils::allocator::tracking::Tracker,
    AreaPolicy: crate::utils::allocator::area::Area,
{
    /// Create a new arena scope.
    pub fn new(arena: &'a mut Arena<A, LockingPolicy, TrackingPolicy, AreaPolicy>) -> Self {
        let rewind_point = arena.get_current();
        Self {
            arena,
            rewind_point,
            finalizers: Vec::new(),
        }
    }

    /// Allocate and construct an object with automatic destruction.
    /// 
    /// For trivially destructible types, this uses the arena's make method.
    /// For non-trivially destructible types, this allocates space for both the object
    /// and a finalizer, ensuring proper cleanup.
    pub fn make<T, Args>(&mut self, args: Args) -> Option<*mut T>
    where
        T: Sized,
        Args: FnOnce() -> T,
    {
        // Check if T is trivially destructible (simplified check)
        if std::mem::needs_drop::<T>() {
            // Non-trivially destructible type - need finalizer
            self.make_with_finalizer(args)
        } else {
            // Trivially destructible type - use arena's make_with
            self.arena.make_with(args)
        }
    }

    /// Allocate and construct an object with a finalizer for non-trivially destructible types.
    fn make_with_finalizer<T, Args>(&mut self, args: Args) -> Option<*mut T>
    where
        T: Sized,
        Args: FnOnce() -> T,
    {
        let size = std::mem::size_of::<T>();
        let alignment = std::mem::align_of::<T>();
        let finalizer_size = std::mem::size_of::<Finalizer>();
        let total_size = size + finalizer_size;

        // Allocate space for both the object and the finalizer
        let ptr = self.arena.alloc(total_size, alignment, 0)?;
        
        // Place the finalizer at the beginning
        let finalizer_ptr = ptr as *mut Finalizer;
        let object_ptr = unsafe { ptr.add(finalizer_size) } as *mut T;

        // Create the finalizer
        unsafe {
            *finalizer_ptr = Finalizer {
                finalizer: Self::destruct::<T>,
                data: object_ptr as *mut u8,
            };
        }

        // Construct the object
        unsafe {
            object_ptr.write(args());
        }

        // Add to finalizer list
        self.finalizers.push(unsafe { *finalizer_ptr });

        Some(object_ptr)
    }

    /// Allocate raw memory within the scope.
    pub fn allocate(&mut self, size: usize, alignment: usize) -> Option<*mut u8> {
        self.arena.alloc(size, alignment, 0)
    }

    /// Allocate typed memory within the scope.
    pub fn allocate_typed<T>(&mut self, count: usize, alignment: usize) -> Option<*mut T>
    where
        T: Sized,
    {
        self.arena.alloc_typed(count, alignment, 0)
    }

    /// Get a reference to the underlying arena.
    pub fn arena(&self) -> &Arena<A, LockingPolicy, TrackingPolicy, AreaPolicy> {
        self.arena
    }

    /// Get a mutable reference to the underlying arena.
    pub fn arena_mut(&mut self) -> &mut Arena<A, LockingPolicy, TrackingPolicy, AreaPolicy> {
        self.arena
    }

    /// Destructor function for a type T.
    fn destruct<T>(ptr: *mut u8) {
        unsafe {
            (ptr as *mut T).drop_in_place();
        }
    }
}

impl<'a, A, LockingPolicy, TrackingPolicy, AreaPolicy> Drop for ArenaScope<'a, A, LockingPolicy, TrackingPolicy, AreaPolicy>
where
    A: Allocator,
    LockingPolicy: Lock<A>,
    TrackingPolicy: crate::utils::allocator::tracking::Tracker,
    AreaPolicy: crate::utils::allocator::area::Area,
{
    fn drop(&mut self) {
        // Run all finalizers in reverse order
        for finalizer in self.finalizers.drain(..).rev() {
            (finalizer.finalizer)(finalizer.data);
        }

        // Rewind the arena to the original point
        self.arena.rewind(self.rewind_point);
    }
}
