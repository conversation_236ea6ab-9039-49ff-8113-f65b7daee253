//! Linear memory allocator implementation.
//!
//! This module provides a linear allocator that allocates memory sequentially
//! from a memory area. It cannot free individual allocations, but can rewind
//! to a previous state or reset completely.

use super::area::Area;
use super::heap::HeapAllocator;
use super::pointermath;
use super::Allocator;

/// A linear allocator that allocates memory sequentially from a memory area.
///
/// Features:
/// - Allocates blocks linearly
/// - Cannot free individual blocks
/// - Can free top of memory back up to a specified point
/// - Doesn't call destructors
pub struct LinearAllocator<A: Area> {
    area: A,
    current: usize,
}

impl<A: Area> LinearAllocator<A> {
    /// Create a new linear allocator with the given memory area.
    pub fn new(area: A) -> Self {
        Self {
            area,
            current: 0,
        }
    }

    /// Get the current allocation pointer.
    pub fn get_current(&self) -> *mut u8 {
        unsafe { pointermath::add_mut(self.area.begin(), self.current) }
    }

    /// Rewind the allocator to the specified pointer.
    ///
    /// # Safety
    ///
    /// The pointer must be within the allocator's memory area and must have been
    /// previously returned by `get_current()`.
    pub unsafe fn rewind(&mut self, ptr: *mut u8) {
        debug_assert!(ptr >= self.area.begin() && ptr < self.area.end());
        self.current = unsafe { ptr.offset_from(self.area.begin()) } as usize;
    }

    /// Reset the allocator, freeing all allocations.
    pub fn reset(&mut self) {
        self.current = 0;
    }

    /// Get the total size of the allocator's memory area.
    pub fn size(&self) -> usize {
        self.area.size()
    }

    /// Get the amount of memory currently allocated.
    pub fn allocated(&self) -> usize {
        self.current
    }

    /// Get the amount of memory still available.
    pub fn available(&self) -> usize {
        self.size().saturating_sub(self.current)
    }
}

impl<A: Area> Allocator for LinearAllocator<A> {
    fn alloc(&mut self, size: usize, alignment: usize, offset: usize) -> Option<*mut u8> {
        if size == 0 {
            return Some(self.get_current());
        }

        let aligned_ptr = unsafe {
            pointermath::align_with_offset_mut(self.area.begin(), alignment, self.current + offset)
        };

        let aligned_offset = unsafe { aligned_ptr.offset_from(self.area.begin()) as usize };
        let end_offset = aligned_offset - offset + size;

        if end_offset <= self.area.size() {
            let result = unsafe { pointermath::add_mut(self.area.begin(), aligned_offset - offset) };
            self.current = end_offset;
            Some(result)
        } else {
            None
        }
    }

    unsafe fn free(&mut self, _ptr: *mut u8, _size: usize) {
        // Linear allocator doesn't free individual allocations
    }

    fn total_size(&self) -> usize {
        self.size()
    }

    fn allocated_size(&self) -> usize {
        self.allocated()
    }
}

/// A linear allocator that falls back to heap allocation when the linear area is exhausted.
pub struct LinearAllocatorWithFallback<A: Area> {
    linear: LinearAllocator<A>,
    heap: HeapAllocator,
    heap_allocations: Vec<(*mut u8, usize)>,
}

impl<A: Area> LinearAllocatorWithFallback<A> {
    /// Create a new linear allocator with fallback using the given memory area.
    pub fn new(area: A) -> Self {
        Self {
            linear: LinearAllocator::new(area),
            heap: HeapAllocator::new(),
            heap_allocations: Vec::new(),
        }
    }

    /// Get the current allocation pointer of the linear allocator.
    pub fn get_current(&self) -> *mut u8 {
        self.linear.get_current()
    }

    /// Rewind the linear allocator to the specified pointer.
    ///
    /// # Safety
    ///
    /// The pointer must be within the linear allocator's memory area and must have been
    /// previously returned by `get_current()`.
    pub unsafe fn rewind(&mut self, ptr: *mut u8) {
        if ptr >= self.linear.area.begin() && ptr < self.linear.area.end() {
            unsafe { self.linear.rewind(ptr); }
        }
    }

    /// Reset the allocator, freeing all allocations.
    pub fn reset(&mut self) {
        self.linear.reset();

        // Free all heap allocations
        for (ptr, size) in self.heap_allocations.drain(..) {
            unsafe {
                self.heap.free(ptr, size);
            }
        }
    }

    /// Check if the given pointer was allocated from the heap.
    pub fn is_heap_allocation(&self, ptr: *mut u8) -> bool {
        ptr < self.linear.area.begin() || ptr >= self.linear.area.end()
    }
}

impl<A: Area> Allocator for LinearAllocatorWithFallback<A> {
    fn alloc(&mut self, size: usize, alignment: usize, offset: usize) -> Option<*mut u8> {
        // Try to allocate from the linear allocator first
        if let Some(ptr) = self.linear.alloc(size, alignment, offset) {
            return Some(ptr);
        }

        // Fall back to heap allocation
        if let Some(ptr) = self.heap.alloc_aligned(size, alignment) {
            self.heap_allocations.push((ptr, size));
            Some(ptr)
        } else {
            None
        }
    }

    unsafe fn free(&mut self, _ptr: *mut u8, _size: usize) {
        // Individual allocations are not freed until reset
    }
}

impl<A: Area> Drop for LinearAllocatorWithFallback<A> {
    fn drop(&mut self) {
        self.reset();
    }
}

#[cfg(test)]
mod tests {
    use crate::utils::allocator::area::StaticArea;

    use super::*;

    const TEST_AREA_SIZE: usize = 1024;

    // Helper function to create a LinearAllocator with a static area for tests.
    fn create_test_linear_allocator(buffer: &mut [u8]) -> LinearAllocator<StaticArea> {
        let area = StaticArea::from_slice(buffer);
        LinearAllocator::new(area)
    }

    #[test]
    fn linear_allocator_new() {
        let mut buffer = [0u8; TEST_AREA_SIZE];
        let allocator = create_test_linear_allocator(&mut buffer);

        assert_eq!(allocator.size(), TEST_AREA_SIZE);
        assert_eq!(allocator.allocated(), 0);
        assert_eq!(allocator.available(), TEST_AREA_SIZE);
    }

    #[test]
    fn linear_allocator_single_alloc() {
        let mut buffer = [0u8; TEST_AREA_SIZE];
        let mut allocator = create_test_linear_allocator(&mut buffer);

        let size = 100;
        let alignment = 8;
        let ptr = allocator.alloc(size, alignment, 0).unwrap();

        assert!(!ptr.is_null());
        assert!(allocator.allocated() >= size);
        assert_eq!(allocator.available(), TEST_AREA_SIZE - allocator.allocated());
    }

    #[test]
    fn linear_allocator_multiple_allocs() {
        let mut buffer = [0u8; TEST_AREA_SIZE];
        let mut allocator = create_test_linear_allocator(&mut buffer);

        let p1 = allocator.alloc(10, 2, 0).unwrap();
        let p2 = allocator.alloc(20, 4, 0).unwrap();

        assert!(!p1.is_null());
        assert!(!p2.is_null());
        // p2 should be at a higher address than p1
        assert!((p2 as usize) > (p1 as usize));
        assert!(allocator.allocated() >= 30);
    }

    #[test]
    fn linear_allocator_alignment() {
        let mut buffer = [0u8; TEST_AREA_SIZE];
        let mut allocator = create_test_linear_allocator(&mut buffer);

        allocator.alloc(3, 1, 0).unwrap(); // Misalign the current pointer
        let allocated_before = allocator.allocated();

        let alignment = 64;
        let ptr = allocator.alloc(10, alignment, 0).unwrap();
        
        // Check if the pointer is aligned
        assert_eq!((ptr as usize) % alignment, 0);
        // Check that space was used for padding
        assert!(allocator.allocated() > allocated_before + 10);
    }

    #[test]
    fn linear_allocator_out_of_memory() {
        let mut buffer = [0u8; 128];
        let mut allocator = create_test_linear_allocator(&mut buffer);

        allocator.alloc(100, 8, 0).unwrap();
        let allocated_before = allocator.allocated();
        
        // This allocation should fail
        let result = allocator.alloc(50, 8, 0);
        assert!(result.is_none());
        // The allocator's state should not change on failure
        assert_eq!(allocator.allocated(), allocated_before);
    }

    #[test]
    fn linear_allocator_reset() {
        let mut buffer = [0u8; TEST_AREA_SIZE];
        let mut allocator = create_test_linear_allocator(&mut buffer);

        allocator.alloc(200, 8, 0).unwrap();
        assert!(allocator.allocated() > 0);

        allocator.reset();
        assert_eq!(allocator.allocated(), 0);
        assert_eq!(allocator.available(), TEST_AREA_SIZE);
        
        // Should be able to allocate again after reset
        let ptr = allocator.alloc(200, 8, 0);
        assert!(ptr.is_some());
    }

    #[test]
    fn linear_allocator_rewind() {
        let mut buffer = [0u8; TEST_AREA_SIZE];
        let mut allocator = create_test_linear_allocator(&mut buffer);

        allocator.alloc(100, 8, 0).unwrap();
        let marker = allocator.get_current();
        let allocated_at_marker = allocator.allocated();

        allocator.alloc(200, 8, 0).unwrap();
        assert!(allocator.allocated() > allocated_at_marker);

        unsafe { allocator.rewind(marker); }
        assert_eq!(allocator.allocated(), allocated_at_marker);

        // Should be able to re-allocate the 200 bytes
        let ptr = allocator.alloc(200, 8, 0);
        assert!(ptr.is_some());
    }

    // --- Tests for LinearAllocatorWithFallback ---

    // Helper to create a fallback allocator for tests.
    fn create_test_fallback_allocator(buffer: &mut [u8]) -> LinearAllocatorWithFallback<StaticArea> {
        let area = StaticArea::from_slice(buffer);
        LinearAllocatorWithFallback::new(area)
    }

    #[test]
    fn fallback_allocator_uses_linear_first() {
        let mut buffer = [0u8; 128];
        let mut allocator = create_test_fallback_allocator(&mut buffer);
        
        let ptr = allocator.alloc(64, 8, 0).unwrap();

        assert!(!allocator.is_heap_allocation(ptr));
        assert!(allocator.heap_allocations.is_empty());
    }

    #[test]
    fn fallback_allocator_falls_back_to_heap() {
        let mut buffer = [0u8; 128];
        let mut allocator = create_test_fallback_allocator(&mut buffer);
        
        // 1. Exhaust the linear area
        allocator.alloc(120, 8, 0).unwrap();
        
        // 2. This allocation must fall back to the heap
        let heap_ptr = allocator.alloc(64, 8, 0).unwrap();

        assert!(allocator.is_heap_allocation(heap_ptr));
        assert_eq!(allocator.heap_allocations.len(), 1);

        // The heap allocation must be freed manually for this test to not leak,
        // but the allocator's Drop will handle it. We can check that here.
        let (p, s) = allocator.heap_allocations[0];
        assert_eq!(p, heap_ptr);
        assert_eq!(s, 64);
    }
    
    #[test]
    fn fallback_allocator_reset_clears_all() {
        let mut buffer = [0u8; 128];
        let mut allocator = create_test_fallback_allocator(&mut buffer);
        
        // Allocate in linear part
        allocator.alloc(100, 8, 0).unwrap();
        // Allocate in heap part
        let heap_ptr = allocator.alloc(50, 8, 0).unwrap();
        
        assert!(allocator.linear.allocated() > 0);
        assert!(allocator.is_heap_allocation(heap_ptr));
        assert!(!allocator.heap_allocations.is_empty());

        allocator.reset();

        assert_eq!(allocator.linear.allocated(), 0);
        assert!(allocator.heap_allocations.is_empty(), "Reset should free and clear heap allocations");
    }

    #[test]
    fn fallback_allocator_drop_cleans_up() {
        let mut buffer = [0u8; 128];
        {
            let mut allocator = create_test_fallback_allocator(&mut buffer);
            // Allocate on the heap
            allocator.alloc(200, 8, 0).unwrap();
            assert_eq!(allocator.heap_allocations.len(), 1);
        } // Allocator is dropped here

        // We can't check the allocator after it's dropped, but this test ensures
        // that the Drop implementation runs without panicking. Tools like Valgrind
        // or memory sanitizers could confirm no memory was leaked.
    }
}