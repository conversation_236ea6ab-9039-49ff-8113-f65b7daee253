//! pool.rs
//! Final version using runtime parameters to provide an ergonomic Object Pool,
//! which is the pragmatic approach in stable Rust.

use super::area::Area;
use super::freelist::{self, AtomicFreeList, FreeList};
use super::heap::HeapAllocator;
use super::{Allocator, AllocatorStats};
use std::marker::PhantomData;
use std::mem::{align_of, size_of};
use std::sync::atomic::{AtomicUsize, Ordering};
use std::ptr;

// --- Base Pool Allocator ---

/// A pool allocator that allocates fixed-size blocks from a memory pool.
/// Its size and alignment are configured at runtime.
pub struct PoolAllocator<A: Area, F = FreeList> {
    pub area: A,
    pub free_list: F,
    pub element_size: usize,
    pub alignment: usize,
    pub total_elements: usize,
    pub allocated_count: AtomicUsize,
    pub failed_allocations: AtomicUsize,
    _marker: PhantomData<F>,
}

// Non-atomic version
impl<A: Area> PoolAllocator<A, FreeList> {
    pub unsafe fn new(
        area: A,
        element_size: usize,
        alignment: usize,
        offset: usize,
    ) -> Self {
        let first_element_offset = unsafe { area.begin().add(offset).align_offset(alignment) };
        let first_element = if first_element_offset == usize::MAX {
            ptr::null_mut()
        } else {
            unsafe { area.begin().add(offset + first_element_offset) }
        };

        let free_list = if first_element.is_null() || first_element >= area.end() {
            unsafe { FreeList::new(ptr::null_mut(), ptr::null_mut(), element_size, alignment, 0) }
        } else {
            unsafe { FreeList::new(first_element, area.end(), element_size, alignment, 0) }
        };

        Self {
            area,
            free_list,
            element_size,
            alignment,
            total_elements: 0,
            allocated_count: AtomicUsize::new(0),
            failed_allocations: AtomicUsize::new(0),
            _marker: PhantomData,
        }
    }
}

// Atomic version
impl<A: Area> PoolAllocator<A, AtomicFreeList> {
    pub unsafe fn new_atomic(
        area: A,
        element_size: usize,
        alignment: usize,
        offset: usize,
    ) -> Self {
        let first_element_offset = unsafe { area.begin().add(offset).align_offset(alignment) };
        let first_element = if first_element_offset == usize::MAX {
            ptr::null_mut()
        } else {
            unsafe { area.begin().add(offset + first_element_offset) }
        };

        let free_list = if first_element.is_null() || first_element >= area.end() {
            unsafe { AtomicFreeList::new(ptr::null_mut(), ptr::null_mut(), element_size, alignment, 0) }
        } else {
            unsafe { AtomicFreeList::new(first_element, area.end(), element_size, alignment, 0) }
        };

        Self {
            area,
            free_list,
            element_size,
            alignment,
            total_elements: 0,
            allocated_count: AtomicUsize::new(0),
            failed_allocations: AtomicUsize::new(0),
            _marker: PhantomData,
        }
    }
}

// --- Ergonomic Object Pool Wrappers ---

/// A helper const function to calculate the required alignment for a block.
const fn object_pool_alignment<T>() -> usize {
    let node_align = align_of::<freelist::Node>();
    let t_align = align_of::<T>();
    if node_align > t_align { node_align } else { t_align }
}

// --- 1. Non-Thread-Safe Object Pool ---

/// A convenient wrapper for a single-threaded pool of same-sized objects of type `T`.
pub struct ObjectPoolAllocator<A: Area, T> {
    inner: PoolAllocator<A, FreeList>,
    _marker: PhantomData<T>,
}

impl<A: Area, T> ObjectPoolAllocator<A, T> {
    /// Creates a new object pool for objects of type `T`.
    pub unsafe fn new(area: A) -> Self {
        let element_size = size_of::<T>();
        let alignment = align_of::<T>();
        Self {
            inner: unsafe { PoolAllocator::new(area, element_size, alignment, 0) },
            _marker: PhantomData,
        }
    }

    /// Allocates a block of memory for one object of type `T`.
    pub fn alloc(&mut self) -> Option<*mut T> {
        let ptr = self.inner.free_list.pop();
        if ptr.is_null() { 
            self.inner.record_failed_allocation();
            None 
        } else { 
            self.inner.record_allocation();
            Some(ptr as *mut T) 
        }
    }

    /// Frees a previously allocated block of memory.
    pub unsafe fn free(&mut self, ptr: *mut T) {
        if !ptr.is_null() {
            self.inner.allocated_count.fetch_sub(1, Ordering::Relaxed);
            unsafe {
                self.inner.free_list.push(ptr as *mut u8);
            }
        }
    }
    
    pub fn object_size(&self) -> usize {
        self.inner.element_size
    }

    /// Get statistics about the pool.
    pub fn stats(&self) -> AllocatorStats {
        AllocatorStats {
            total_size: self.inner.total_elements() * self.inner.element_size,
            allocated_size: self.inner.allocated_elements() * self.inner.element_size,
            available_size: self.inner.available_elements() * self.inner.element_size,
            peak_allocated: self.inner.allocated_elements() * self.inner.element_size, // Simplified
            allocation_count: self.inner.allocated_elements(),
            failed_allocations: self.inner.failed_allocations(),
        }
    }
}

impl<A: Area, T> Allocator for ObjectPoolAllocator<A, T> {
    fn alloc(&mut self, size: usize, alignment: usize, _offset: usize) -> Option<*mut u8> {
        if size != self.object_size() || alignment != object_pool_alignment::<T>() {
            return None; // Pool only supports fixed size/alignment
        }
        self.alloc().map(|p| p as *mut u8)
    }

    unsafe fn free(&mut self, ptr: *mut u8, size: usize) {
        if !ptr.is_null() && size == size_of::<T>() {
            unsafe {
                self.free(ptr as *mut T);
            }
        }
    }

    fn total_size(&self) -> usize {
        self.inner.total_elements() * self.inner.element_size
    }

    fn allocated_size(&self) -> usize {
        self.inner.allocated_elements() * self.inner.element_size
    }
}

// --- 2. Thread-Safe Object Pool (The Missing Part) ---

/// A convenient wrapper for a thread-safe, lock-free pool of objects of type `T`.
pub struct ThreadSafeObjectPoolAllocator<A: Area, T> {
    inner: PoolAllocator<A, AtomicFreeList>,
    _marker: PhantomData<T>,
}

// So it can be shared across threads using an Arc
unsafe impl<A: Area + Send, T: Send> Send for ThreadSafeObjectPoolAllocator<A, T> {}
unsafe impl<A: Area + Sync, T: Sync> Sync for ThreadSafeObjectPoolAllocator<A, T> {}

impl<A: Area, T> ThreadSafeObjectPoolAllocator<A, T> {
    /// Creates a new thread-safe object pool.
    pub unsafe fn new(area: A) -> Self {
        let element_size = size_of::<T>();
        let alignment = object_pool_alignment::<T>();
        Self {
            inner: unsafe { PoolAllocator::new_atomic(area, element_size, alignment, 0) },
            _marker: PhantomData,
        }
    }

    /// Atomically allocates a block of memory. This method is thread-safe.
    /// Note that it takes `&self`, not `&mut self`.
    pub fn alloc(&self) -> Option<*mut T> {
        let ptr = self.inner.free_list.pop();
        if ptr.is_null() { None } else { Some(ptr as *mut T) }
    }

    /// Atomically frees a previously allocated block. This method is thread-safe.
    pub unsafe fn free(&self, ptr: *mut T) {
        if !ptr.is_null() {
            self.inner.allocated_count.fetch_sub(1, Ordering::Relaxed);
            unsafe {
                self.inner.free_list.push(ptr as *mut u8);
            }
        }
    }
    
    pub fn object_size(&self) -> usize {
        self.inner.element_size
    }
}

// --- 3. Pool Allocator With Fallback ---

/// A pool allocator that falls back to heap allocation when the pool is exhausted.
pub struct PoolAllocatorWithFallback<A: Area, T> {
    pool: ObjectPoolAllocator<A, T>,
    heap: HeapAllocator,
    heap_allocations: Vec<(*mut T, usize)>,
}

impl<A: Area, T> PoolAllocatorWithFallback<A, T> {
    /// Creates a new pool allocator with fallback.
    pub unsafe fn new(area: A) -> Self {
        Self {
            pool: unsafe { ObjectPoolAllocator::new(area) },
            heap: HeapAllocator::new(),
            heap_allocations: Vec::new(),
        }
    }

    /// Allocates a block of memory, falling back to heap if pool is exhausted.
    pub fn alloc(&mut self) -> Option<*mut T> {
        // Try pool first
        if let Some(ptr) = self.pool.alloc() {
            return Some(ptr);
        }

        // Fall back to heap
        let size = std::mem::size_of::<T>();
        let alignment = object_pool_alignment::<T>();
        if let Some(ptr) = self.heap.aligned_alloc(size, alignment) {
            let ptr = ptr as *mut T;
            self.heap_allocations.push((ptr, size));
            Some(ptr)
        } else {
            None
        }
    }

    /// Frees a previously allocated block.
    pub unsafe fn free(&mut self, ptr: *mut T) {
        if !ptr.is_null() {
            if self.is_heap_allocation(ptr) {
                let size = size_of::<T>();
                let alignment = align_of::<T>();
                unsafe {
                    self.heap.aligned_free(ptr as *mut u8, size, alignment);
                }
                // Remove from heap allocations list
                self.heap_allocations.retain(|&(p, _)| p != ptr);
            } else {
                unsafe {
                    self.pool.free(ptr);
                }
            }
        }
    }

    /// Check if the given pointer was allocated from the heap.
    pub fn is_heap_allocation(&self, ptr: *mut T) -> bool {
        self.heap_allocations.iter().any(|&(p, _)| p == ptr)
    }

    /// Reset the allocator, freeing all heap allocations.
    pub fn reset(&mut self) {
        // Free all heap allocations
        for (ptr, size) in self.heap_allocations.drain(..) {
            let alignment = object_pool_alignment::<T>();
            unsafe {
                self.heap.aligned_free(ptr as *mut u8, size, alignment);
            }
        }
    }

    /// Get the object size.
    pub fn object_size(&self) -> usize {
        self.pool.object_size()
    }
}

impl<A: Area, T> Drop for PoolAllocatorWithFallback<A, T> {
    fn drop(&mut self) {
        self.reset();
    }
}

// --- 4. Thread-Safe Pool Allocator With Fallback ---

/// A thread-safe pool allocator that falls back to heap allocation.
pub struct ThreadSafePoolAllocatorWithFallback<A: Area, T> {
    pool: ThreadSafeObjectPoolAllocator<A, T>,
    heap: HeapAllocator,
    heap_allocations: std::sync::Mutex<Vec<(*mut T, usize)>>,
}

// Safe to send and share across threads
unsafe impl<A: Area + Send, T: Send> Send for ThreadSafePoolAllocatorWithFallback<A, T> {}
unsafe impl<A: Area + Sync, T: Sync> Sync for ThreadSafePoolAllocatorWithFallback<A, T> {}

impl<A: Area, T> ThreadSafePoolAllocatorWithFallback<A, T> {
    /// Creates a new thread-safe pool allocator with fallback.
    pub unsafe fn new(area: A) -> Self {
        Self {
            pool: unsafe { ThreadSafeObjectPoolAllocator::new(area) },
            heap: HeapAllocator::new(),
            heap_allocations: std::sync::Mutex::new(Vec::new()),
        }
    }

    /// Allocates a block of memory, falling back to heap if pool is exhausted.
    /// This method is thread-safe.
    pub fn alloc(&self) -> Option<*mut T> {
        // Try pool first
        if let Some(ptr) = self.pool.alloc() {
            return Some(ptr);
        }

        // Fall back to heap (this needs to be synchronized)
        let size = std::mem::size_of::<T>();
        let alignment = object_pool_alignment::<T>();
        
        // We need to create a temporary heap allocator for this allocation
        let mut temp_heap = HeapAllocator::new();
        if let Some(ptr) = temp_heap.aligned_alloc(size, alignment) {
            let ptr = ptr as *mut T;
            if let Ok(mut allocations) = self.heap_allocations.lock() {
                allocations.push((ptr, size));
            }
            Some(ptr)
        } else {
            None
        }
    }

    /// Frees a previously allocated block. This method is thread-safe.
    pub unsafe fn free(&self, ptr: *mut T) {
        if !ptr.is_null() {
            if self.is_heap_allocation(ptr) {
                let size = size_of::<T>();
                let alignment = align_of::<T>();
                let mut temp_heap = HeapAllocator::new();
                unsafe {
                    temp_heap.aligned_free(ptr as *mut u8, size, alignment);
                }
                // Remove from heap allocations list
                if let Ok(mut allocations) = self.heap_allocations.lock() {
                    allocations.retain(|&(p, _)| p != ptr);
                }
            } else {
                unsafe {
                    self.pool.free(ptr);
                }
            }
        }
    }

    /// Check if the given pointer was allocated from the heap.
    pub fn is_heap_allocation(&self, ptr: *mut T) -> bool {
        if let Ok(allocations) = self.heap_allocations.lock() {
            allocations.iter().any(|&(p, _)| p == ptr)
        } else {
            false
        }
    }

    /// Get the object size.
    pub fn object_size(&self) -> usize {
        self.pool.object_size()
    }
}

// Common methods for both versions
impl<A: Area, F> PoolAllocator<A, F> {
    /// Get the total number of elements in the pool.
    pub fn total_elements(&self) -> usize {
        self.total_elements
    }

    /// Get the number of currently allocated elements.
    pub fn allocated_elements(&self) -> usize {
        self.allocated_count.load(Ordering::Relaxed)
    }

    /// Get the number of available elements.
    pub fn available_elements(&self) -> usize {
        self.total_elements.saturating_sub(self.allocated_elements())
    }

    /// Get the number of failed allocations.
    pub fn failed_allocations(&self) -> usize {
        self.failed_allocations.load(Ordering::Relaxed)
    }

    /// Get the utilization percentage.
    pub fn utilization_percentage(&self) -> f64 {
        if self.total_elements == 0 {
            0.0
        } else {
            (self.allocated_elements() as f64 / self.total_elements as f64) * 100.0
        }
    }

    /// Record a successful allocation.
    pub fn record_allocation(&self) {
        self.allocated_count.fetch_add(1, Ordering::Relaxed);
    }

    /// Record a successful deallocation.
    pub fn record_deallocation(&self) {
        self.allocated_count.fetch_sub(1, Ordering::Relaxed);
    }

    /// Record a failed allocation.
    pub fn record_failed_allocation(&self) {
        self.failed_allocations.fetch_add(1, Ordering::Relaxed);
    }
}

// ---------------------------------------------------------------------------------
// ---------------------------- UNIT TESTS -----------------------------------------
// ---------------------------------------------------------------------------------
#[cfg(test)]
mod tests {
    use super::*;
    use crate::utils::allocator::area::Area;
    use std::sync::Arc;
    use std::thread;

    struct TestArea { begin: *mut u8, end: *mut u8 }
    impl Area for TestArea {
        fn begin(&self) -> *mut u8 { self.begin }
        fn end(&self) -> *mut u8 { self.end }
    }
    // Required for the thread-safe tests, as the Area will be shared.
    unsafe impl Send for TestArea {}
    unsafe impl Sync for TestArea {}

    const TEST_POOL_SIZE: usize = 4096;

    #[derive(Debug, PartialEq, Eq)]
    #[repr(C, align(64))]
    struct MyObject { id: u128, name: [u8; 32] }

    #[test]
    fn test_object_pool_allocator() {
        let mut buffer = [0u8; TEST_POOL_SIZE];
        let area = TestArea {
            begin: buffer.as_mut_ptr(),
            end: unsafe { buffer.as_mut_ptr().add(TEST_POOL_SIZE) },
        };
        let mut pool = unsafe { ObjectPoolAllocator::<TestArea, MyObject>::new(area) };

        let p1 = pool.alloc().expect("First allocation failed");
        assert!(!p1.is_null());
        unsafe { pool.free(p1); }
    }

    // --- New Test for ThreadSafeObjectPoolAllocator ---

    #[test]
    fn test_thread_safe_object_pool_concurrently() {
        // Use a heap-allocated buffer to get a stable pointer for threading.
        let mut buffer = vec![0u8; TEST_POOL_SIZE].into_boxed_slice();
        let area = TestArea {
            begin: buffer.as_mut_ptr(),
            end: unsafe { buffer.as_mut_ptr().add(TEST_POOL_SIZE) },
        };

        // Create the thread-safe pool and wrap it in an Arc.
        let pool = Arc::new(unsafe {
            ThreadSafeObjectPoolAllocator::<TestArea, MyObject>::new(area)
        });

        let mut handles = vec![];
        let num_threads = 4;
        let iterations_per_thread = 500;

        for _ in 0..num_threads {
            let pool_clone = Arc::clone(&pool);
            handles.push(thread::spawn(move || {
                let mut local_vec = Vec::new();
                // Churn: repeatedly allocate and deallocate
                for i in 0..iterations_per_thread {
                    if let Some(p) = pool_clone.alloc() {
                        // In a real app, do work here...
                        unsafe { (*p).id = i as u128; }
                        local_vec.push(p);
                    } else {
                        // Pool might be temporarily empty, try to free one.
                        if let Some(p_to_free) = local_vec.pop() {
                            unsafe { pool_clone.free(p_to_free); }
                        }
                    }
                }
                // Free any remaining allocations
                for p in local_vec {
                    unsafe { pool_clone.free(p); }
                }
            }));
        }

        for handle in handles {
            handle.join().unwrap();
        }

        // Final check: exhaust the pool to ensure all objects were returned.
        let mut final_count = 0;
        while let Some(_) = pool.alloc() {
            final_count += 1;
        }

        let expected_count_approx = TEST_POOL_SIZE / size_of::<MyObject>();
        assert!((final_count as i32 - expected_count_approx as i32).abs() <= 2);
        
        println!("Thread-safe pool churn test passed. All {} blocks accounted for.", final_count);
    }

    // --- Tests for PoolAllocatorWithFallback ---

    #[test]
    fn test_pool_allocator_with_fallback() {
        let mut buffer = [0u8; 128]; // Small buffer to force fallback
        let area = TestArea {
            begin: buffer.as_mut_ptr(),
            end: unsafe { buffer.as_mut_ptr().add(128) },
        };
        let mut pool = unsafe { PoolAllocatorWithFallback::<TestArea, MyObject>::new(area) };

        // Allocate more objects than the pool can hold
        let mut allocations = Vec::new();
        for i in 0..10 {
            if let Some(ptr) = pool.alloc() {
                unsafe { (*ptr).id = i as u128; }
                allocations.push(ptr);
            }
        }

        // Some should be from pool, some from heap
        let pool_allocations = allocations.iter().filter(|&&p| !pool.is_heap_allocation(p)).count();
        let heap_allocations = allocations.iter().filter(|&&p| pool.is_heap_allocation(p)).count();

        assert!(pool_allocations > 0, "Should have some pool allocations");
        assert!(heap_allocations > 0, "Should have some heap allocations");

        // Free all allocations
        for ptr in allocations {
            unsafe { pool.free(ptr); }
        }

        // Reset should clean up heap allocations
        pool.reset();
    }

    #[test]
    fn test_thread_safe_pool_allocator_with_fallback() {
        let mut buffer = vec![0u8; 128].into_boxed_slice();
        let area = TestArea {
            begin: buffer.as_mut_ptr(),
            end: unsafe { buffer.as_mut_ptr().add(128) },
        };
        let pool = Arc::new(unsafe {
            ThreadSafePoolAllocatorWithFallback::<TestArea, MyObject>::new(area)
        });

        let mut handles = vec![];
        let num_threads = 4;
        let iterations_per_thread = 100;

        for _ in 0..num_threads {
            let pool_clone = Arc::clone(&pool);
            handles.push(thread::spawn(move || {
                let mut local_vec = Vec::new();
                for _ in 0..iterations_per_thread {
                    if let Some(p) = pool_clone.alloc() {
                        local_vec.push(p);
                    }
                }
                // Free all allocations
                for p in local_vec {
                    unsafe { pool_clone.free(p); }
                }
            }));
        }

        for handle in handles {
            handle.join().unwrap();
        }
    }
}