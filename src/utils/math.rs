use nalgebra_glm as glm;

/// Perspective matrix that is suitable for Vulkan.
///
/// It inverts the projected y-axis. And set the depth range to 0..1
/// instead of -1..1. Mind the vertex winding order though.
pub fn perspective(fovy_degrees: f32, aspect: f32, near: f32, far: f32) -> glm::Mat4 {
    let mut proj = glm::perspective_rh_zo(aspect, fovy_degrees.to_radians(), near, far);

    proj[(1, 1)] *= -1.0;

    proj
}
